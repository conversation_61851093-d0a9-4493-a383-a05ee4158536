import { Skeleton } from "@/components/ui/skeleton";
import { Card } from "@/components/ui/card";

export const PayslipSectionSkeleton = () => {
  return (
    <div className="space-y-4">
      {/* Section Header Skeleton */}
      <div className="flex items-center justify-between">
        <Skeleton className="h-6 w-32" />
        <div className="flex gap-2">
          <Skeleton className="h-8 w-20" />
          <Skeleton className="h-8 w-20" />
        </div>
      </div>

      {/* Section Content Skeleton */}
      <div className="space-y-2">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="flex items-center space-x-4 px-1.5">
            <Skeleton className="h-5 w-5" />
            <div className="flex-grow">
              <div className="grid grid-cols-[148px_110px_98px_150px] items-center gap-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-7 w-full" />
                <Skeleton className="h-4 w-full" />
                <div className="flex items-center gap-2">
                  <Skeleton className="h-4 w-4" />
                  <Skeleton className="h-4 w-4" />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export const BasicPaySectionSkeleton = () => {
  return (
    <div className="space-y-4">
      {/* Section Header Skeleton */}
      <div className="flex items-center justify-between">
        <Skeleton className="h-6 w-24" />
        <div className="flex gap-2">
          <Skeleton className="h-8 w-16" />
          <Skeleton className="h-8 w-16" />
          <Skeleton className="h-8 w-16" />
          <Skeleton className="h-8 w-20" />
          <Skeleton className="h-8 w-20" />
        </div>
      </div>

      {/* Salary Elements Skeleton */}
      <div className="space-y-1">
        {[...Array(2)].map((_, i) => (
          <div
            key={`salary-${i}`}
            className="flex items-center space-x-4 px-1.5"
          >
            <Skeleton className="h-5 w-5" />
            <div className="flex-grow">
              <div className="grid grid-cols-[148px_110px_98px_150px] items-center gap-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-7 w-full" />
                <Skeleton className="h-4 w-full" />
                <div className="flex items-center gap-2">
                  <Skeleton className="h-4 w-4" />
                  <Skeleton className="h-4 w-4" />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Daily/Hourly Elements Skeleton */}
      <div className="space-y-2">
        {[...Array(2)].map((_, i) => (
          <Card key={`daily-hourly-${i}`} className="p-2">
            <div className="flex items-center space-x-4">
              <Skeleton className="h-5 w-5" />
              <div className="flex-grow">
                <div className="grid grid-cols-[75px_65px_140px_60px_150px] items-center gap-1">
                  <Skeleton className="h-7 w-full" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-7 w-full" />
                  <Skeleton className="h-7 w-full" />
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-4 w-4" />
                    <Skeleton className="h-4 w-4" />
                  </div>
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};

export const AdditionsSectionSkeleton = () => {
  return (
    <div className="space-y-4">
      {/* Section Header Skeleton */}
      <div className="flex items-center justify-between">
        <Skeleton className="h-6 w-20" />
        <div className="flex gap-2">
          <Skeleton className="h-8 w-20" />
          <Skeleton className="h-8 w-20" />
        </div>
      </div>

      {/* Addition Items Skeleton */}
      <div className="space-y-2">
        {[...Array(2)].map((_, i) => (
          <div key={i} className="flex items-center space-x-4 px-1.5">
            <Skeleton className="h-5 w-5" />
            <div className="flex-grow">
              <div className="grid grid-cols-[200px_110px_150px] items-center gap-2">
                <Skeleton className="h-7 w-full" />
                <Skeleton className="h-7 w-full" />
                <div className="flex items-center gap-2">
                  <Skeleton className="h-4 w-4" />
                  <Skeleton className="h-4 w-4" />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export const DeductionsSectionSkeleton = () => {
  return (
    <div className="space-y-4">
      {/* Section Header Skeleton */}
      <div className="flex items-center justify-between">
        <Skeleton className="h-6 w-24" />
        <div className="flex gap-2">
          <Skeleton className="h-8 w-20" />
          <Skeleton className="h-8 w-20" />
        </div>
      </div>

      {/* Deduction Items Skeleton */}
      <div className="space-y-2">
        {[...Array(2)].map((_, i) => (
          <div key={i} className="flex items-center space-x-4 px-1.5">
            <Skeleton className="h-5 w-5" />
            <div className="flex-grow">
              <div className="grid grid-cols-[200px_110px_150px] items-center gap-2">
                <Skeleton className="h-7 w-full" />
                <Skeleton className="h-7 w-full" />
                <div className="flex items-center gap-2">
                  <Skeleton className="h-4 w-4" />
                  <Skeleton className="h-4 w-4" />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export const PayslipEditorSkeleton = () => {
  return (
    <div className="flex h-full w-full gap-2">
      {/* Left sidebar skeleton */}
      <div style={{ width: "235px", flexShrink: 0 }}>
        <Card className="h-full p-1.5">
          <div className="mb-1 flex justify-center gap-1">
            <Skeleton className="h-8 w-20" />
            <Skeleton className="h-8 w-20" />
          </div>
          <div className="space-y-1">
            {[...Array(5)].map((_, i) => (
              <Skeleton key={i} className="h-8 w-full" />
            ))}
          </div>
        </Card>
      </div>

      {/* Main content skeleton */}
      <div className="flex-1">
        <Card className="flex flex-col overflow-hidden p-3">
          <div className="flex-grow overflow-y-auto pr-3">
            {/* Header skeleton */}
            <div className="mb-4 flex items-center gap-3">
              <Skeleton className="h-6 w-64" />
              <Skeleton className="h-6 w-16" />
            </div>

            {/* Sections skeleton */}
            <div className="space-y-6">
              <BasicPaySectionSkeleton />
              <AdditionsSectionSkeleton />
              <DeductionsSectionSkeleton />
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};
