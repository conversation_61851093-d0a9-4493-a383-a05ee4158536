"use client";

import { EMPLOYER_DB_LOCALSTORAGE_PREFIX } from "@/constants/file";

import React, { useState, useMemo, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { useEmployeesForPeriod } from "@/hooks/tanstack-query/useEmployeesForPeriod";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { LayoutGrid, Edit, Lock } from "lucide-react";

// Import period data and payslip section components

import PayslipAdditionsSection from "@/components/payroll/payslip-sections/additions-section";
import PayslipDeductionsSection from "@/components/payroll/payslip-sections/deductions-section";
import PayslipStatutorySection from "@/components/payroll/payslip-sections/statutory-section";
import PayslipPensionSection from "@/components/payroll/payslip-sections/pension-section";
import PayslipNotesSection from "@/components/payroll/payslip-sections/notes-section";
import PayslipSummary from "@/components/payroll/payslip-sections/summary-section";
import EmployeeList from "@/components/payroll/employee-list";
import { PayslipBasicSection } from "@/components/payroll/payslip-sections/basic-section";
import { PayslipReadOnlyProvider } from "@/providers/payslip-readonly-provider";
import { usePayslip } from "@/hooks/tanstack-query/usePayslip";

import type { PayPeriod } from "@/drizzle/schema/employer/payPeriod";

// Utility function to get ordinal suffix for a number
const getOrdinalSuffix = (day: number): string => {
  if (day >= 11 && day <= 13) {
    return "th";
  }
  switch (day % 10) {
    case 1:
      return "st";
    case 2:
      return "nd";
    case 3:
      return "rd";
    default:
      return "th";
  }
};

// Utility function to format date with ordinal suffix
const formatDateWithOrdinal = (dateString: string): string => {
  const date = new Date(dateString);
  const day = date.getDate();
  const month = date.toLocaleDateString("en-GB", { month: "long" });
  const year = date.getFullYear();

  return `${day}${getOrdinalSuffix(day)} ${month} ${year}`;
};

// Utility function to convert period type to singular form
const getPeriodTypeSingular = (type: string): string => {
  switch (type.toLowerCase()) {
    case "weekly":
      return "Week";
    case "two_weekly":
      return "Two Week";
    case "four_weekly":
      return "Four Week";
    case "monthly":
      return "Month";
    case "quarterly":
      return "Quarter";
    case "yearly":
      return "Year";
    default:
      return type.charAt(0).toUpperCase() + type.slice(1);
  }
};

export interface PayslipEditorProps {
  periodId: string;
  employeeId: string | null;
  onBackToOverview?: () => void;
  onSwitchToBatch?: () => void;
  payPeriods: PayPeriod[];
}

const PayslipEditor: React.FC<PayslipEditorProps> = ({
  periodId,
  employeeId,
  onBackToOverview,
  onSwitchToBatch,
  payPeriods,
}) => {
  const [activeEmployee, setActiveEmployee] = useState<string | null>(
    employeeId,
  );
  // Track the originally intended employee selection to restore when they become available
  const [intendedEmployee, setIntendedEmployee] = useState<string | null>(
    employeeId,
  );

  const employees = useEmployeesForPeriod(periodId);

  // Fetch real payslip data
  const { data: realPayslipData, refetch: refetchPayslip } = usePayslip(
    activeEmployee || "",
    periodId,
  );

  // Get the actual payslip status from real data, default to 'open'
  const actualPayslipStatus = realPayslipData?.payslip?.status || "open";

  // Sync intended employee when employeeId prop changes (from parent component)
  useEffect(() => {
    if (employeeId) {
      setIntendedEmployee(employeeId);
      setActiveEmployee(employeeId);
    }
  }, [employeeId]);

  // Handle employee selection persistence across period changes
  useEffect(() => {
    if (employees.length > 0) {
      // If we have an intended employee but no active employee, try to restore the intended one
      if (intendedEmployee && !activeEmployee) {
        const intendedEmployeeExists = employees.some(
          (emp) => emp.id === intendedEmployee,
        );
        if (intendedEmployeeExists) {
          setActiveEmployee(intendedEmployee);
        }
      }
      // If we have an active employee, check if they exist in the current period
      else if (activeEmployee) {
        const employeeExistsInPeriod = employees.some(
          (emp) => emp.id === activeEmployee,
        );
        if (!employeeExistsInPeriod) {
          // Employee doesn't exist in this period, clear active but keep intended
          setActiveEmployee(null);
        }
      }
    }
  }, [activeEmployee, intendedEmployee, employees]);

  // Refetch payslip data when employee changes to ensure fresh status
  useEffect(() => {
    if (activeEmployee) {
      refetchPayslip();
    }
  }, [activeEmployee, refetchPayslip]);

  // Find the selected period and get its type using useMemo for better performance
  const selectedPeriod = useMemo(() => {
    return payPeriods.find((p) => p.id === periodId);
  }, [payPeriods, periodId]);

  // Get period type from selected period
  const periodType = useMemo(() => {
    if (selectedPeriod) {
      // Convert period type to proper case for display (e.g., "Weekly" instead of "weekly")
      return (
        selectedPeriod.type.charAt(0).toUpperCase() +
        selectedPeriod.type.slice(1)
      );
    }
    return "Monthly"; // Default fallback
  }, [selectedPeriod]);

  // Handle employee selection
  const handleEmployeeSelect = (id: string) => {
    setActiveEmployee(id);
    setIntendedEmployee(id); // Remember this as the intended selection
    // In a real implementation, we would fetch the payslip data for this employee
  };

  // If no employee is selected, show a message
  if (!activeEmployee) {
    return (
      <div className="mt-1 flex w-full gap-2">
        {/* Left sidebar - Employee list */}
        <div style={{ width: "235px", flexShrink: 0 }}>
          <Card className="p-1.5">
            <div className="mb-1 flex justify-center gap-1">
              <Button
                size="sm"
                variant="default"
                onClick={onBackToOverview}
                className=""
              >
                <LayoutGrid className="mr-1 size-4 text-white" />
                Overview
              </Button>
              <Button
                size="sm"
                variant="default"
                onClick={onSwitchToBatch}
                className=""
              >
                <Edit className="mr-1 size-4 text-white" />
                Batch Edit
              </Button>
            </div>
            <EmployeeList
              employees={employees}
              periodId={periodId}
              onEmployeeSelect={handleEmployeeSelect}
            />
          </Card>
        </div>

        {/* Main content - No employee selected message */}
        <div className="flex-1">
          <Card className="flex flex-col overflow-hidden p-3">
            <div className="flex flex-1 items-center justify-center">
              <div className="text-center">
                <h2 className="text-muted-foreground mb-4 text-xl font-semibold">
                  No employee selected
                </h2>
                <p className="text-muted-foreground max-w-md">
                  Please select an employee from the list on the left to view
                  their payslip.
                </p>
              </div>
            </div>
          </Card>
        </div>

        {/* Right sidebar - Empty space to maintain layout */}
        <div className="w-[250px] flex-shrink-0 px-1">
          <Card className="p-3">
            <div className="text-muted-foreground text-center text-sm">
              Select an employee to view summary
            </div>
          </Card>
        </div>
      </div>
    );
  }

  // Find the selected employee
  const selectedEmployee = employees.find((emp) => emp.id === activeEmployee);

  // Generate the payslip header text
  const getPayslipHeaderText = (): string => {
    if (!selectedEmployee || !selectedPeriod) {
      return `${selectedEmployee?.name || "Employee"} - Payslip for ${periodId.replace(/-/g, " ")}`;
    }

    const employeeName = selectedEmployee.name;
    const periodTypeSingular = getPeriodTypeSingular(selectedPeriod.type);
    const formattedEndDate = formatDateWithOrdinal(selectedPeriod.period_end);

    return `${employeeName} - ${periodTypeSingular} Ending ${formattedEndDate}`;
  };

  return (
    <div className="mt-1 flex w-full gap-2">
      {/* Left sidebar - Employee list */}
      <div style={{ width: "235px", flexShrink: 0 }}>
        <Card className="p-1.5">
          <div className="mb-1 flex justify-center gap-1">
            <Button
              size="sm"
              variant="default"
              onClick={onBackToOverview}
              className=""
            >
              <LayoutGrid className="mr-1 size-4 text-white" />
              Overview
            </Button>
            <Button
              size="sm"
              variant="default"
              onClick={onSwitchToBatch}
              className=""
            >
              <Edit className="mr-1 size-4 text-white" />
              Batch Edit
            </Button>
          </div>
          <EmployeeList
            employees={employees}
            periodId={periodId}
            selectedEmployeeId={activeEmployee}
            onEmployeeSelect={handleEmployeeSelect}
          />
        </Card>
      </div>

      {/* Main content - Payslip editor */}
      <div className="flex-1">
        <Card className="flex flex-col overflow-hidden p-3">
          <div
            className="flex-grow overflow-y-auto pr-3"
            style={{
              height: `calc(96vh - ${localStorage.getItem(`${EMPLOYER_DB_LOCALSTORAGE_PREFIX}_period_selector_visible`) === "true" ? 300 : 150}px)`,
              minHeight: "300px",
            }}
          >
            <div className="flex items-center gap-3">
              <h2 className="text-foreground dark:text-foreground text-lg font-normal">
                {getPayslipHeaderText()}
              </h2>
              {actualPayslipStatus === "closed" && (
                <div className="flex items-center gap-1 rounded-md bg-slate-100 px-2 py-1 dark:bg-slate-800">
                  <Lock className="h-4 w-4 text-slate-600 dark:text-slate-400" />
                  <span className="text-sm font-medium text-slate-600 dark:text-slate-400">
                    Closed
                  </span>
                </div>
              )}
            </div>

            <Separator className="my-4" />

            <PayslipReadOnlyProvider payslipStatus={actualPayslipStatus}>
              {/* Pay Section */}
              {activeEmployee && (
                <PayslipBasicSection
                  employeeId={activeEmployee}
                  periodId={periodId}
                  periodType={periodType}
                />
              )}

              <Separator className="my-6" />

              {/* Additions Section */}
              {activeEmployee && (
                <PayslipAdditionsSection
                  employeeId={activeEmployee}
                  periodId={periodId}
                />
              )}

              <Separator className="my-2" />

              {/* Deductions Section */}
              {activeEmployee && (
                <PayslipDeductionsSection
                  employeeId={activeEmployee}
                  periodId={periodId}
                />
              )}

              <Separator className="my-6" />

              {/* Statutory Pay Section */}
              <PayslipStatutorySection
                key={`statutory-${activeEmployee}-${periodId}`}
                data={{ ssp: 0, smp: 0, spp: 0, shpp: 0 }}
                onChange={(data) => {
                  // TODO: Wire up to database when statutory section is connected
                  console.log("Statutory data changed:", data);
                }}
              />

              <Separator className="my-6" />

              {/* Pension Section */}
              <PayslipPensionSection
                key={`pension-${activeEmployee}-${periodId}`}
                data={{
                  employeeContribution: 0,
                  employerContribution: 0,
                  isPercentage: true,
                  employeePercentage: 5,
                  employerPercentage: 10,
                }}
                onChange={(data) => {
                  // TODO: Wire up to database when pension section is connected
                  console.log("Pension data changed:", data);
                }}
              />

              <Separator className="my-6" />

              {/* Notes Section */}
              <PayslipNotesSection
                key={`notes-${activeEmployee}-${periodId}`}
                employeeId={activeEmployee || ""}
                periodId={periodId}
              />
            </PayslipReadOnlyProvider>
          </div>
        </Card>
      </div>

      {/* Right sidebar - Summary */}
      <div className="w-[250px] flex-shrink-0 px-1">
        <PayslipSummary
          calculations={{
            taxablePay: 0,
            tax: 0,
            studentLoan: 0,
            postgraduateLoan: 0,
            employeeNIC: 0,
            employerNIC: 0,
            netPay: 0,
            additionsToNetPay: 0,
            deductionsFromNetPay: 0,
            employeePension: 0,
            employerPension: 0,
            takeHomePay: 0,
            employerCost: 0,
          }}
          ytd={{
            gross: 0,
            taxablePay: 0,
            tax: 0,
            nationalInsurance: 0,
            employeePension: 0,
            employerPension: 0,
            netPay: 0,
          }}
        />
      </div>
    </div>
  );
};

export default PayslipEditor;
